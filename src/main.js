import './assets/main.css'

import { createApp } from 'vue'
import App from './App.vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import router from './router'
import { checkPermissionDirective } from './utils/permission'

const app = createApp(App)

// 注册 Element Plus
app.use(ElementPlus)

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 注册权限指令
app.directive('permission', {
  mounted(el, binding) {
    const hasPermission = checkPermissionDirective(binding.value)
    if (!hasPermission) {
      el.style.display = 'none'
    }
  },
  updated(el, binding) {
    const hasPermission = checkPermissionDirective(binding.value)
    if (!hasPermission) {
      el.style.display = 'none'
    } else {
      el.style.display = ''
    }
  }
})

// 使用路由
app.use(router)

app.mount('#app')
