/**
 * 权限配置文件
 * 集中管理所有权限定义和角色配置
 * 使用 JSON 数据格式，便于维护和扩展
 */

// 权限配置数据 (JSON 格式)
const permissionsConfig = {
  // 权限常量定义
  "permissions": {
    // 控制台权限
    "DASHBOARD_VIEW": "dashboard:view",
    
    // 用户管理权限
    "USER_VIEW": "user:view",
    "USER_CREATE": "user:create",
    "USER_EDIT": "user:edit",
    "USER_DELETE": "user:delete",
    "USER_EXPORT": "user:export",
    "USER_IMPORT": "user:import",

    // 订单管理权限
    "ORDER_VIEW": "order:view",
    "ORDER_CREATE": "order:create",
    "ORDER_EDIT": "order:edit",
    "ORDER_DELETE": "order:delete",
    "ORDER_EXPORT": "order:export",

    // 商品管理权限
    "PRODUCT_VIEW": "product:view",
    "PRODUCT_CREATE": "product:create",
    "PRODUCT_EDIT": "product:edit",
    "PRODUCT_DELETE": "product:delete",
    "PRODUCT_EXPORT": "product:export",

    // 配置管理权限
    "CONFIG_VIEW": "config:view",
    "CONFIG_CREATE": "config:create",
    "CONFIG_EDIT": "config:edit",
    "CONFIG_DELETE": "config:delete",

    // 后台用户管理权限
    "ADMIN_USER_VIEW": "admin_user:view",
    "ADMIN_USER_CREATE": "admin_user:create",
    "ADMIN_USER_EDIT": "admin_user:edit",
    "ADMIN_USER_DELETE": "admin_user:delete",
    
    // 角色管理权限
    "ROLE_VIEW": "role:view",
    "ROLE_CREATE": "role:create",
    "ROLE_EDIT": "role:edit",
    "ROLE_DELETE": "role:delete",
    
    // 权限管理
    "PERMISSION_VIEW": "permission:view",
    "PERMISSION_ASSIGN": "permission:assign",
    
    // 系统管理权限
    "SYSTEM_VIEW": "system:view",
    "SYSTEM_CONFIG": "system:config",
    "SYSTEM_LOG": "system:log",
    "SYSTEM_BACKUP": "system:backup",
    "SYSTEM_MONITOR": "system:monitor",
    
    // 数据统计权限
    "STATS_VIEW": "stats:view",
    "STATS_EXPORT": "stats:export",
    "STATS_ANALYSIS": "stats:analysis",
    
    // 组件管理权限
    "FORM_VIEW": "form:view",
    "FORM_MANAGE": "form:manage",
    "TABLE_VIEW": "table:view",
    "TABLE_MANAGE": "table:manage",
    
    // 内容管理权限
    "CONTENT_VIEW": "content:view",
    "CONTENT_CREATE": "content:create",
    "CONTENT_EDIT": "content:edit",
    "CONTENT_DELETE": "content:delete",
    "CONTENT_PUBLISH": "content:publish",
    
    // 文件管理权限
    "FILE_VIEW": "file:view",
    "FILE_UPLOAD": "file:upload",
    "FILE_DELETE": "file:delete",
    "FILE_DOWNLOAD": "file:download"
  },

  // 权限分组配置
  "permissionGroups": {
    "dashboard": {
      "name": "控制台",
      "permissions": [
        "dashboard:view",
        "stats:view"
      ]
    },
    "user": {
      "name": "用户管理",
      "permissions": [
        "user:view",
        "user:create",
        "user:edit",
        "user:delete",
        "user:export",
        "user:import"
      ]
    },
    "order": {
      "name": "订单管理",
      "permissions": [
        "order:view",
        "order:create",
        "order:edit",
        "order:delete",
        "order:export"
      ]
    },
    "product": {
      "name": "商品管理",
      "permissions": [
        "product:view",
        "product:create",
        "product:edit",
        "product:delete",
        "product:export"
      ]
    },
    "config": {
      "name": "配置管理",
      "permissions": [
        "config:view",
        "config:create",
        "config:edit",
        "config:delete"
      ]
    },
    "admin_user": {
      "name": "后台用户管理",
      "permissions": [
        "admin_user:view",
        "admin_user:create",
        "admin_user:edit",
        "admin_user:delete"
      ]
    },
    "role": {
      "name": "角色管理",
      "permissions": [
        "role:view",
        "role:create",
        "role:edit",
        "role:delete",
        "permission:view",
        "permission:assign"
      ]
    },
    "system": {
      "name": "系统管理",
      "permissions": [
        "system:view",
        "system:config",
        "system:log",
        "system:backup",
        "system:monitor"
      ]
    },
    "component": {
      "name": "组件管理",
      "permissions": [
        "form:view",
        "form:manage",
        "table:view",
        "table:manage"
      ]
    },
    "content": {
      "name": "内容管理",
      "permissions": [
        "content:view",
        "content:create",
        "content:edit",
        "content:delete",
        "content:publish"
      ]
    },
    "file": {
      "name": "文件管理",
      "permissions": [
        "file:view",
        "file:upload",
        "file:delete",
        "file:download"
      ]
    },
    "stats": {
      "name": "数据统计",
      "permissions": [
        "stats:view",
        "stats:export",
        "stats:analysis"
      ]
    }
  },

  // 角色权限配置
  "rolePermissions": {
    // 超级管理员 - 拥有所有权限
    "superadmin": [
      "dashboard:view", "user:view", "user:create", "user:edit", "user:delete", "user:export", "user:import",
      "order:view", "order:create", "order:edit", "order:delete", "order:export",
      "product:view", "product:create", "product:edit", "product:delete", "product:export",
      "config:view", "config:create", "config:edit", "config:delete",
      "admin_user:view", "admin_user:create", "admin_user:edit", "admin_user:delete",
      "role:view", "role:create", "role:edit", "role:delete", "permission:view", "permission:assign",
      "system:view", "system:config", "system:log", "system:backup", "system:monitor",
      "stats:view", "stats:export", "stats:analysis", "form:view", "form:manage", "table:view", "table:manage",
      "content:view", "content:create", "content:edit", "content:delete", "content:publish",
      "file:view", "file:upload", "file:delete", "file:download"
    ],
    
    // 管理员 - 除系统核心配置外的所有权限
    "admin": [
      "dashboard:view", "stats:view", "stats:export", "stats:analysis",
      "user:view", "user:create", "user:edit", "user:delete", "user:export", "user:import",
      "order:view", "order:create", "order:edit", "order:delete", "order:export",
      "product:view", "product:create", "product:edit", "product:delete", "product:export",
      "config:view", "config:create", "config:edit", "config:delete",
      "admin_user:view", "admin_user:create", "admin_user:edit", "admin_user:delete",
      "role:view", "role:create", "role:edit", "role:delete", "permission:view", "permission:assign",
      "system:view", "system:log", "system:monitor",
      "form:view", "form:manage", "table:view", "table:manage",
      "content:view", "content:create", "content:edit", "content:delete", "content:publish",
      "file:view", "file:upload", "file:delete", "file:download"
    ],
    
    // 编辑员 - 内容和用户管理权限
    "editor": [
      "dashboard:view", "stats:view",
      "user:view", "user:edit",
      "order:view", "order:edit",
      "product:view", "product:edit",
      "config:view",
      "form:view", "form:manage", "table:view", "table:manage",
      "content:view", "content:create", "content:edit", "content:publish",
      "file:view", "file:upload", "file:download"
    ],
    
    // 内容管理员 - 专注内容相关
    "content_manager": [
      "dashboard:view", "stats:view",
      "content:view", "content:create", "content:edit", "content:delete", "content:publish",
      "file:view", "file:upload", "file:delete", "file:download"
    ],
    
    // 普通用户 - 只有查看权限
    "user": [
      "dashboard:view", "stats:view", "user:view",
      "form:view", "table:view", "content:view",
      "file:view", "file:download"
    ],
    
    // 访客 - 最基本的查看权限
    "guest": [
      "dashboard:view", "stats:view"
    ]
  },

  // 角色配置信息
  "roleConfig": {
    "superadmin": {
      "name": "超级管理员",
      "description": "拥有系统所有权限，包括系统核心配置",
      "level": 1,
      "color": "#ff4d4f"
    },
    "admin": {
      "name": "管理员",
      "description": "拥有大部分管理权限，负责日常管理工作",
      "level": 2,
      "color": "#fa8c16"
    },
    "editor": {
      "name": "编辑员",
      "description": "拥有内容编辑和用户管理权限",
      "level": 3,
      "color": "#1890ff"
    },
    "content_manager": {
      "name": "内容管理员",
      "description": "专门负责内容管理相关工作",
      "level": 4,
      "color": "#52c41a"
    },
    "user": {
      "name": "普通用户",
      "description": "基本的查看权限，适合一般员工",
      "level": 5,
      "color": "#722ed1"
    },
    "guest": {
      "name": "访客",
      "description": "最基本的访问权限",
      "level": 6,
      "color": "#8c8c8c"
    }
  },

  // 权限名称映射
  "permissionNames": {
    "dashboard:view": "查看控制台",
    "user:view": "查看用户",
    "user:create": "创建用户",
    "user:edit": "编辑用户",
    "user:delete": "删除用户",
    "user:export": "导出用户",
    "user:import": "导入用户",
    "order:view": "查看订单",
    "order:create": "创建订单",
    "order:edit": "编辑订单",
    "order:delete": "删除订单",
    "order:export": "导出订单",
    "product:view": "查看商品",
    "product:create": "创建商品",
    "product:edit": "编辑商品",
    "product:delete": "删除商品",
    "product:export": "导出商品",
    "config:view": "查看配置",
    "config:create": "创建配置",
    "config:edit": "编辑配置",
    "config:delete": "删除配置",
    "admin_user:view": "查看后台用户",
    "admin_user:create": "创建后台用户",
    "admin_user:edit": "编辑后台用户",
    "admin_user:delete": "删除后台用户",
    "role:view": "查看角色",
    "role:create": "创建角色",
    "role:edit": "编辑角色",
    "role:delete": "删除角色",
    "permission:view": "查看权限",
    "permission:assign": "分配权限",
    "system:view": "查看系统",
    "system:config": "系统配置",
    "system:log": "系统日志",
    "system:backup": "系统备份",
    "system:monitor": "系统监控",
    "stats:view": "查看统计",
    "stats:export": "导出统计",
    "stats:analysis": "数据分析",
    "form:view": "查看表单",
    "form:manage": "管理表单",
    "table:view": "查看表格",
    "table:manage": "管理表格",
    "content:view": "查看内容",
    "content:create": "创建内容",
    "content:edit": "编辑内容",
    "content:delete": "删除内容",
    "content:publish": "发布内容",
    "file:view": "查看文件",
    "file:upload": "上传文件",
    "file:delete": "删除文件",
    "file:download": "下载文件"
  },

  // 默认配置
  "defaultConfig": {
    // 默认角色
    "DEFAULT_ROLE": "user",
    
    // 公开权限（无需登录）
    "PUBLIC_PERMISSIONS": [],
    
    // 登录后默认权限
    "AUTHENTICATED_PERMISSIONS": [
      "dashboard:view"
    ],
    
    // 权限继承关系（子权限包含父权限）
    "PERMISSION_INHERITANCE": {
      "user:edit": ["user:view"],
      "user:delete": ["user:view"],
      "order:edit": ["order:view"],
      "order:delete": ["order:view"],
      "product:edit": ["product:view"],
      "product:delete": ["product:view"],
      "config:edit": ["config:view"],
      "config:delete": ["config:view"],
      "admin_user:edit": ["admin_user:view"],
      "admin_user:delete": ["admin_user:view"],
      "role:edit": ["role:view"],
      "role:delete": ["role:view"],
      "content:edit": ["content:view"],
      "content:delete": ["content:view"],
      "content:publish": ["content:edit"],
      "form:manage": ["form:view"],
      "table:manage": ["table:view"]
    }
  }
}

// 导出权限常量 - 保持向后兼容
export const PERMISSIONS = Object.entries(permissionsConfig.permissions).reduce((acc, [key, value]) => {
  acc[key] = value
  return acc
}, {})

// 导出权限分组配置 - 保持向后兼容
export const PERMISSION_GROUPS = Object.entries(permissionsConfig.permissionGroups).reduce((acc, [key, value]) => {
  acc[key] = {
    ...value,
    permissions: value.permissions.map(p => PERMISSIONS[Object.keys(PERMISSIONS).find(k => PERMISSIONS[k] === p)] || p)
  }
  return acc
}, {})

// 导出角色权限配置 - 保持向后兼容
export const ROLE_PERMISSIONS = permissionsConfig.rolePermissions

// 导出角色配置信息 - 保持向后兼容
export const ROLE_CONFIG = permissionsConfig.roleConfig

// 导出权限名称映射 - 保持向后兼容
export const PERMISSION_NAMES = permissionsConfig.permissionNames

// 导出默认配置 - 保持向后兼容
export const DEFAULT_CONFIG = permissionsConfig.defaultConfig

// 导出原始 JSON 配置供高级用法使用
export const RAW_CONFIG = permissionsConfig

// 默认导出整个配置
export default permissionsConfig
